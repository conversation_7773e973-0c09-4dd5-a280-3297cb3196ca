import { Button } from "@/components/ui/button";
import { GitBranch, ExternalLink } from "lucide-react";
import { useState } from "react";

const ProjectsSection = () => {
  const [expandedSkills, setExpandedSkills] = useState<{ [key: number]: boolean }>({});

  const toggleSkills = (projectId: number) => {
    setExpandedSkills(prev => ({
      ...prev,
      [projectId]: !prev[projectId]
    }));
  };

  const projects = [
    {
      id: 1,
      title: "Promptify",
      description: "Full-stack AI prompt builder with modular design, API integrations, and smooth deployment features",
      techStack: ["React", "TypeScripts", "Node.js", "Razorpay", "OpenAI / Anthropic API integration"],
      githubUrl: "https://github.com/CodehubPriyanshu/Promptify",
      liveUrl: "https://promptify-henna-zeta.vercel.app/",
      featured: true
    },
    {
      id: 2,
      title: "SentimentSage",
      description: "AI sentiment analysis app with React frontend, Flask backend, secure configs.",
      techStack: ["React", "MongoDB Atlas", "flask", "Axios", "JWT Authentication"],
      githubUrl: "https://github.com/CodehubPriyanshu/SentimentSage",
      liveUrl: "https://sentiment-sage42984.vercel.app/",
      featured: true
    },
    {
      id: 3,
      title: "Sales Insights",
      description: "Interactive Tableau dashboard with SQL insights for tracking revenue, profit, and market performance",
      techStack: ["Interactive Tableau Dashboards", "SQL Data Analysis", "Data Modeling", "Star Schema",],
      githubUrl: "https://github.com/CodehubPriyanshu/Sales-Data-Analysis-and-Insights",
      liveUrl: "https://social-insights.herokuapp.com",
      featured: false
    },
    {
      id: 4,
      title: "Personal Finance Dashboard",
      description: "Interactive Power BI dashboard analyzing spending trends, categories, and habits with Python-cleaned data.",
      techStack: ["Microsoft Power BI Desktop", "Python", "Data Collection & Cleaning", "Power BI Modeling", "Dashboard Visualizations"],
      githubUrl: "https://github.com/CodehubPriyanshu/Finance-PowerBI-Dashboard",
      liveUrl: "https://weather-predict.streamlit.app",
      featured: false
    },
    {
      id: 5,
      title: "Real-time Chat App",
      description: "Real-time React Firebase chat app with authentication, media sharing, notifications, and responsive design",
      techStack: ["React 18", "ESLint + Prettier", "Firebase", "Vercel "],
      githubUrl: "https://github.com/CodehubPriyanshu/Chat-Application",
      liveUrl: "https://chat-realtime.vercel.app",
      featured: false
    },
    {
      id: 6,
      title: "HireHub-Job Portal",
      description: "MERN-based job portal with search, tracking, role-based access, secure authentication, and cloud deployment.",
      techStack: ["React Router", "Cloudinary", "React Toastify", "Nodemailer", "bcrypt"],
      githubUrl: "https://github.com/CodehubPriyanshu/HireHub-JobPortal-",
      liveUrl: "https://hire-hub-job-portal-es2a-m70kt2t27-codehubpriyanshus-projects.vercel.app/",
      featured: true
    }
  ];

  const handleProjectClick = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <section id="projects" className="mobile-section relative">
      {/* Background effects */}
      <div className="absolute top-20 left-4 sm:left-10 w-48 sm:w-72 h-48 sm:h-72 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-4 sm:right-10 w-64 sm:w-96 h-64 sm:h-96 bg-accent-glow/10 rounded-full blur-3xl"></div>
      
      <div className="mobile-container mx-auto">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="mobile-heading mb-4">
            <span className="neon-text-glow">Featured Projects</span>
          </h2>
          <p className="mobile-subheading text-muted-foreground">Innovative solutions built with modern technologies</p>
        </div>

        {/* Mobile-first responsive grid - 2 cards per row on mobile */}
        <div className="mobile-card-grid">
          {projects.map((project, index) => (
            <div
              key={project.id}
              className="mobile-card touch-card group animate-fade-in-up"
              style={{ animationDelay: `${index * 100}ms` }}
              onClick={() => {/* Card click handler can be added here */}}
            >
              <div className="mobile-card-header">
                <h3 className="mobile-card-title group-hover:neon-text">
                  {project.title}
                </h3>
                <p className="mobile-card-description mt-2">
                  {project.description}
                </p>
              </div>

              <div className="mobile-card-content">
                {/* Tech Stack with mobile-optimized badges */}
                <div className="flex flex-wrap gap-1.5 sm:gap-2">
                  {(expandedSkills[project.id] ? project.techStack : project.techStack.slice(0, 2)).map((tech) => (
                    <span
                      key={tech}
                      className="mobile-badge"
                    >
                      {tech}
                    </span>
                  ))}
                  {project.techStack.length > 2 && (
                    <button
                      className="mobile-badge cursor-pointer hover:bg-primary/10 transition-all duration-300 touch-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleSkills(project.id);
                      }}
                      aria-label={expandedSkills[project.id] ? 'Show fewer technologies' : `Show ${project.techStack.length - 2} more technologies`}
                    >
                      {expandedSkills[project.id]
                        ? 'Less'
                        : `+${project.techStack.length - 2}`
                      }
                    </button>
                  )}
                </div>

                {/* Action buttons optimized for mobile */}
                <div className="flex gap-2 mt-auto pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 touch-button text-xs sm:text-sm py-2 px-3"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleProjectClick(project.githubUrl);
                    }}
                    aria-label={`View source code for ${project.title}`}
                  >
                    <GitBranch className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                    Code
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 touch-button text-xs sm:text-sm py-2 px-3"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleProjectClick(project.liveUrl);
                    }}
                    aria-label={`View live demo of ${project.title}`}
                  >
                    <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                    Live
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-8 sm:mt-12">
          <Button 
            variant="outline" 
            className="btn-neon text-sm sm:text-base"
            onClick={() => handleProjectClick('https://github.com/CodehubPriyanshu?tab=repositories')}
          >
            <GitBranch className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
            View All Projects on GitHub
          </Button>
        </div>
      </div>
    </section>
  );
};

export default ProjectsSection;
