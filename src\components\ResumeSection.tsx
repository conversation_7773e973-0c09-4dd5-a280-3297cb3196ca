import { GraduationCap, Calendar, MapPin } from "lucide-react";

const ResumeSection = () => {
  const education = [
    {
      level: "Graduated",
      course: "Bachelor of Computer Applications (BCA)",
      institution: "ITM University",
      location: "Gwalior, Madhya Pradesh",
      year: "2022 - 2025",
      subjects: ["Software Engineering", "Data Analytics", "Database Management", "Web Development", "AI/ML Fundamentals"]
    },
    {
      level: "Higher Secondary (12th)",
      course: "Commerce",
      institution: "Kendriya Vidyalaya No.3",
      location: "Gwalior, Madhya Pradesh", 
      year: "2021 - 2022",
      subjects: ["Accountancy", "Business Studies", "Economics", "English", "Informatics Practices"]
    },
    {
      level: "Secondary (10th)",
      course: "All Subjects",
      institution: "Kendriya Vidyalaya No.3 ",
      location: "Gwalior, Madhya Pradesh",
      year: "2019 - 2020",
      subjects: ["Mathematics", "Science", "Social Science", "English", "Hindi"]
    }
  ];

  return (
    <section id="resume" className="mobile-section relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-20 sm:top-40 left-4 sm:left-20 w-48 sm:w-64 h-48 sm:h-64 bg-primary/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 sm:bottom-20 right-4 sm:right-32 w-56 sm:w-80 h-56 sm:h-80 bg-accent-glow/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>
      
      <div className="mobile-container mx-auto relative z-10">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="mobile-heading mb-4">
            <span className="neon-text-glow">Education</span>
          </h2>
          <p className="mobile-subheading text-muted-foreground">Academic journey</p>
        </div>

        {/* Mobile-first responsive grid layout */}
        <div className="max-w-6xl mx-auto mobile-card-grid">
          {education.map((edu, index) => (
            <div
              key={index}
              className="mobile-card touch-card group animate-fade-in-up"
              style={{ animationDelay: `${index * 200}ms` }}
            >
              <div className="mobile-card-header">
                <div className="flex items-center gap-2 sm:gap-3 mb-3">
                  <div className="bg-gradient-primary rounded-full p-1.5 sm:p-2">
                    <GraduationCap className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 text-primary-foreground" />
                  </div>
                  <span className="mobile-badge font-semibold">
                    {edu.level}
                  </span>
                </div>

                <h3 className="mobile-card-title group-hover:neon-text mb-2">
                  {edu.course}
                </h3>

                <div className="space-y-1.5 sm:space-y-2">
                  <div className="text-xs sm:text-sm md:text-base">
                    <span className="font-semibold text-foreground">{edu.institution}</span>
                  </div>
                  <div className="flex flex-col gap-1 sm:gap-2 text-xs sm:text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span>{edu.year}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="truncate">{edu.location}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mobile-card-content">
                <div>
                  <h4 className="font-semibold mb-2 sm:mb-3 text-xs sm:text-sm md:text-base">Key Subjects</h4>
                  <div className="flex flex-wrap gap-1 sm:gap-1.5">
                    {edu.subjects.slice(0, 4).map((subject) => (
                      <span
                        key={subject}
                        className="mobile-badge"
                      >
                        {subject}
                      </span>
                    ))}
                    {edu.subjects.length > 4 && (
                      <span className="mobile-badge">
                        +{edu.subjects.length - 4}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}

        </div>

        {/* Timeline connector - hidden on mobile */}
        <div className="absolute left-1/2 top-32 bottom-32 w-px bg-gradient-to-b from-primary via-accent-glow to-primary opacity-20 transform -translate-x-1/2 hidden xl:block"></div>
      </div>
    </section>
  );
};

export default ResumeSection;