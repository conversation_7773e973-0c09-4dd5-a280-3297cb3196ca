import { Button } from "@/components/ui/button";
import { Briefcase, Building2, ExternalLink } from "lucide-react";
import { experiences } from "@/data/experienceData";

const ExperienceSection = () => {
  const items = experiences || [];

  const handleCompanyClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <section id="experience" className="mobile-section relative overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute top-20 left-4 w-48 h-48 bg-primary/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-4 w-56 h-56 bg-accent-glow/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>

      <div className="mobile-container mx-auto relative z-10">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="mobile-heading mb-4">
            <span className="neon-text-glow">Professional Experience</span>
          </h2>
          <p className="mobile-subheading text-muted-foreground">Roles and responsibilities</p>
        </div>

        {/* Mobile-first responsive grid - 2 cards per row on mobile */}
        <div className="max-w-6xl mx-auto mobile-card-grid">
          {items.map((exp, idx) => (
            <div
              key={idx}
              className="mobile-card touch-card group animate-fade-in-up"
              style={{ animationDelay: `${idx * 100}ms` }}
            >
              <div className="mobile-card-header">
                <div className="flex items-center justify-between gap-2 mb-3">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <div className="bg-gradient-primary rounded-full p-1.5 sm:p-2 flex-shrink-0">
                      <Briefcase className="h-3 w-3 sm:h-4 sm:w-4 text-primary-foreground" />
                    </div>
                    <span className="mobile-badge text-xs font-semibold truncate">{exp.company}</span>
                  </div>

                  {/* Company Website Link with Icon */}
                  {exp.companyUrl && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="touch-button p-1.5 sm:p-2 flex-shrink-0"
                      onClick={() => handleCompanyClick(exp.companyUrl!)}
                      aria-label={`Visit ${exp.company} website`}
                    >
                      <Building2 className="h-3 w-3 sm:h-4 sm:w-4" />
                      <ExternalLink className="h-2 w-2 sm:h-3 sm:w-3 ml-0.5" />
                    </Button>
                  )}
                </div>

                <h3 className="mobile-card-title group-hover:neon-text mb-2">
                  {exp.role}
                </h3>
                <div className="mobile-card-meta">{exp.duration}</div>
              </div>

              <div className="mobile-card-content">
                <ul className="space-y-1.5 sm:space-y-2 list-disc pl-4 sm:pl-5">
                  {(exp.responsibilities || []).map((item, i) => (
                    <li key={i} className="text-xs sm:text-sm text-muted-foreground leading-relaxed">
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ExperienceSection;
